package com.maguo.loan.cash.flow.service;

import com.jinghang.capital.api.dto.BankChannel;
import com.jinghang.capital.api.dto.test.DBCrudDto;
import com.jinghang.common.util.IdGen;
import com.maguo.loan.cash.flow.entity.BankRepayRecord;
import com.maguo.loan.cash.flow.entity.Credit;
import com.maguo.loan.cash.flow.entity.CustomRepayRecord;
import com.maguo.loan.cash.flow.entity.Loan;
import com.maguo.loan.cash.flow.entity.LoanRecord;
import com.maguo.loan.cash.flow.entity.RepayPlan;
import com.maguo.loan.cash.flow.entity.WithholdFlow;
import com.maguo.loan.cash.flow.entity.WithholdShareInfo;
import com.maguo.loan.cash.flow.enums.ChargeBizType;
import com.maguo.loan.cash.flow.enums.FlowChannel;
import com.maguo.loan.cash.flow.enums.PaymentChannel;
import com.maguo.loan.cash.flow.enums.ProcessState;
import com.maguo.loan.cash.flow.enums.RepayMode;
import com.maguo.loan.cash.flow.enums.RepayPurpose;
import com.maguo.loan.cash.flow.enums.RepayState;
import com.maguo.loan.cash.flow.enums.WhetherState;
import com.maguo.loan.cash.flow.remote.core.FinTestService;
import com.maguo.loan.cash.flow.repository.BankRepayRecordRepository;
import com.maguo.loan.cash.flow.repository.CreditRepository;
import com.maguo.loan.cash.flow.repository.CustomRepayRecordRepository;
import com.maguo.loan.cash.flow.repository.LoanRecordRepository;
import com.maguo.loan.cash.flow.repository.LoanRepository;
import com.maguo.loan.cash.flow.repository.RepayPlanRepository;
import com.maguo.loan.cash.flow.repository.WithholdFlowRepository;
import com.maguo.loan.cash.flow.repository.WithholdShareInfoRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 验证 授信、放款、还款核心表的 CRUD没有问题
 * 调用 capital 接口 执行相同的操作
 *
 * <AUTHOR>
 * @date 2025-08-21 16:05
 */
@Service
public class TestService {
    @Autowired
    private FinTestService finTestService;


    @Autowired
    private CreditRepository creditRepository;
    @Autowired
    private LoanRecordRepository loanRecordRepository;
    @Autowired
    private LoanRepository loanRepository;
    @Autowired
    private RepayPlanRepository repayPlanRepository;

    // 还款相关Repository
    @Autowired
    private CustomRepayRecordRepository customRepayRecordRepository;
    @Autowired
    private BankRepayRecordRepository bankRepayRecordRepository;
    @Autowired
    private WithholdFlowRepository withholdFlowRepository;
    @Autowired
    private WithholdShareInfoRepository withholdShareInfoRepository;

    public static void main(String[] args) {
        String test = IdGen.genId("TEST", 10);
        System.out.println(test);
    }

    @Transactional
    public String test(String id) {
        // 生成测试ID
        String testId = "TEST0000001";

        try {
            // ====== Credit ======
            Credit credit = new Credit();
            credit.setId(testId);
            credit.setUserId("user123");
            credit.setOrderId("order001");
            credit.setCreditAmt(new BigDecimal("20000.00"));
            credit.setPeriods(12);
            credit.setIrrRate(new BigDecimal("0.10"));
            credit.setApplyTime(LocalDateTime.now());
            credit.setState(ProcessState.FAILED);
            credit.setCreatedBy("system");
            credit.setBankChannel(BankChannel.CYBK);
            credit.setFlowChannel(FlowChannel.LVXIN);
            credit.setCreatedTime(LocalDateTime.now());
            creditRepository.save(credit);

            credit.setState(ProcessState.FAILED);
            credit.setUpdatedBy("admin");
            credit.setUpdatedTime(LocalDateTime.now());
            creditRepository.save(credit);

            creditRepository.findById(credit.getId())
                .ifPresent(c -> System.out.println("Credit 查询成功: " + c.getState()));
            creditRepository.deleteById(credit.getId());


            // ====== LoanRecord ======
            LoanRecord loanRecord = new LoanRecord();
            loanRecord.setId(testId);
            loanRecord.setUserId("user123");
            loanRecord.setOrderId("order001");
            loanRecord.setAmount(new BigDecimal("15000.00"));
            loanRecord.setLoanTime(LocalDateTime.now());
            loanRecord.setLoanState(ProcessState.FAILED);
            loanRecord.setCreatedBy("system");
            loanRecord.setCreatedTime(LocalDateTime.now());
            loanRecordRepository.save(loanRecord);

            loanRecord.setLoanState(ProcessState.P);
            loanRecord.setUpdatedBy("admin");
            loanRecord.setUpdatedTime(LocalDateTime.now());
            loanRecordRepository.save(loanRecord);

            loanRecordRepository.findById(loanRecord.getId())
                .ifPresent(lr -> System.out.println("LoanRecord 查询成功: " + lr.getLoanState()));
            loanRecordRepository.deleteById(loanRecord.getId());


            // ====== Loan ======
            Loan loan = new Loan();
            loan.setId(testId);
            loan.setUserId("user123");
            loan.setOrderId("order001");
            loan.setCreditId("credit001");
            loan.setAmount(new BigDecimal("12000.00"));
            loan.setPackageId("packid123");
            loan.setPeriods(12);
            loan.setApplyTime(LocalDateTime.now());
            loan.setLoanState(ProcessState.FAILED);
            loan.setPlanSyncCore(WhetherState.Y);
            loan.setCreatedBy("system");

            loan.setCreatedTime(LocalDateTime.now());
            loanRepository.save(loan);

            loan.setLoanState(ProcessState.P);
            loan.setUpdatedBy("admin");
            loan.setUpdatedTime(LocalDateTime.now());
            loanRepository.save(loan);

            loanRepository.findById(loan.getId())
                .ifPresent(l -> System.out.println("Loan 查询成功: " + l.getLoanState()));
            loanRepository.deleteById(loan.getId());

            // ====== RepayPlan ======
            RepayPlan repayPlan = new RepayPlan();
            repayPlan.setId(testId);
            repayPlan.setUserId("user123");
            repayPlan.setLoanId("loan001");
            repayPlan.setPeriod(1);
            repayPlan.setAmount(new BigDecimal("1000.00"));
            repayPlan.setPrincipalAmt(new BigDecimal("500.00"));
            repayPlan.setInterestAmt(new BigDecimal("50.00"));
            repayPlan.setGuaranteeAmt(new BigDecimal("500.00"));
            repayPlan.setPenaltyAmt(new BigDecimal("500.00"));
            repayPlan.setConsultFee(new BigDecimal("500.00"));
            repayPlan.setPlanRepayDate(LocalDateTime.now().plusMonths(1).toLocalDate());
            repayPlan.setCustRepayState(RepayState.NORMAL);
            repayPlan.setCreatedBy("system");
            repayPlan.setCreatedTime(LocalDateTime.now());
            repayPlanRepository.save(repayPlan);

            repayPlan.setCustRepayState(RepayState.REPAID);
            repayPlan.setUpdatedBy("admin");
            repayPlan.setUpdatedTime(LocalDateTime.now());
            repayPlanRepository.save(repayPlan);

            repayPlanRepository.findById(repayPlan.getId())
                .ifPresent(rp -> System.out.println("RepayPlan 查询成功: " + rp.getCustRepayState()));
            repayPlanRepository.deleteById(repayPlan.getId());

            // ====== CustomRepayRecord ======
            CustomRepayRecord customRepay = new CustomRepayRecord();
            customRepay.setId(testId);
            customRepay.setLoanId("loan001");
            customRepay.setPeriod(1);
            customRepay.setRepayApplyDate(LocalDateTime.now());
            customRepay.setTotalAmt(new BigDecimal("600.00"));
            customRepay.setPenaltyAmt(new BigDecimal("5.00"));
            customRepay.setGuaranteeAmt(new BigDecimal("15.00"));
            customRepay.setInterestAmt(new BigDecimal("50.00"));
            customRepay.setPrincipalAmt(new BigDecimal("500.00"));
            customRepay.setBreachAmt(new BigDecimal("5.00"));
            customRepay.setActPlatformPenaltyAmt(new BigDecimal("5.00"));
            customRepay.setRepayPurpose(RepayPurpose.CURRENT);
            customRepay.setNeedTwiceState(WhetherState.Y);

            customRepay.setCreatedBy("system");
            customRepay.setCreatedTime(LocalDateTime.now());
            customRepayRecordRepository.save(customRepay);

            customRepay.setRemark("提前还款");
            customRepay.setUpdatedBy("admin");
            customRepay.setUpdatedTime(LocalDateTime.now());
            customRepayRecordRepository.save(customRepay);

            customRepayRecordRepository.findById(customRepay.getId())
                .ifPresent(cr -> System.out.println("CustomRepayRecord 查询成功: " + cr.getRemark()));
            customRepayRecordRepository.deleteById(customRepay.getId());

            // ====== BankRepayRecord ======
            BankRepayRecord bankRepay = new BankRepayRecord();
            bankRepay.setId(testId);
            bankRepay.setLoanId("loan001");
            bankRepay.setSourceRecordId("test111");
            bankRepay.setPeriod(1);
            bankRepay.setRepayPurpose(RepayPurpose.CURRENT);
            bankRepay.setRepayMode(RepayMode.OFFLINE);
            bankRepay.setAmount(new BigDecimal("800.00"));
            bankRepay.setPrincipal(new BigDecimal("500.00"));
            bankRepay.setInterest(new BigDecimal("50.00"));
            bankRepay.setPenalty(new BigDecimal("10.00"));
            bankRepay.setGuarantee(new BigDecimal("50.00"));
            bankRepay.setConsultFee(new BigDecimal("50.00"));
            bankRepay.setBreach(new BigDecimal("50.00"));

            bankRepay.setRepayTime(LocalDateTime.now());
            bankRepay.setState(ProcessState.SUCCEED);
            bankRepay.setCreatedBy("system");
            bankRepay.setCreatedTime(LocalDateTime.now());
            bankRepayRecordRepository.save(bankRepay);

            bankRepay.setState(ProcessState.FAILED);
            bankRepay.setUpdatedBy("admin");
            bankRepay.setUpdatedTime(LocalDateTime.now());
            bankRepayRecordRepository.save(bankRepay);

            bankRepayRecordRepository.findById(bankRepay.getId())
                .ifPresent(br -> System.out.println("BankRepayRecord 查询成功: " + br.getState()));
            bankRepayRecordRepository.deleteById(bankRepay.getId());

            // ====== WithholdFlow ======
            WithholdFlow flow = new WithholdFlow();
            flow.setId(testId);
            flow.setLoanId("loan001");
            flow.setRepayRecordId("test111");
            flow.setPayAmount(new BigDecimal("1200.00"));
            flow.setPayState(ProcessState.SUCCEED);
            flow.setPayOrderNo("test111");
            flow.setAgreementNo("test111");
            flow.setBizType(ChargeBizType.FINANCE);
            flow.setChannelId(PaymentChannel.BAOFU);
            flow.setChannelMchId("CMI001");
            flow.setCommonWithholdType("test111");
            flow.setMotherDeductionNumber("test111");
            flow.setPeriod(1);
            flow.setRemark("test222");

            flow.setCreatedBy("system");
            flow.setCreatedTime(LocalDateTime.now());
            withholdFlowRepository.save(flow);

            flow.setRemark("SUCCESS");
            flow.setUpdatedBy("admin");
            flow.setUpdatedTime(LocalDateTime.now());
            withholdFlowRepository.save(flow);

            withholdFlowRepository.findById(flow.getId())
                .ifPresent(f -> System.out.println("WithholdFlow 查询成功: " + f.getRemark()));
            withholdFlowRepository.deleteById(flow.getId());

            // ====== WithholdShareInfo ======
            WithholdShareInfo shareInfo = new WithholdShareInfo();
            shareInfo.setId(testId);
            shareInfo.setWithholdId("test111");
            shareInfo.setMerchantNo("test111");
            shareInfo.setPrincipal(new BigDecimal("500.00"));
            shareInfo.setInterest(new BigDecimal("50.00"));
            shareInfo.setPenalty(new BigDecimal("50.00"));
            shareInfo.setGuarantee(new BigDecimal("50.00"));
            shareInfo.setConsult(new BigDecimal("50.00"));
            shareInfo.setBreach(new BigDecimal("50.00"));

            shareInfo.setAmount(new BigDecimal("600.00"));
            shareInfo.setCreatedBy("system");
            shareInfo.setCreatedTime(LocalDateTime.now());
            withholdShareInfoRepository.save(shareInfo);

            shareInfo.setRemark("分账记录");
            shareInfo.setUpdatedBy("admin");
            shareInfo.setUpdatedTime(LocalDateTime.now());
            withholdShareInfoRepository.save(shareInfo);

            withholdShareInfoRepository.findById(shareInfo.getId())
                .ifPresent(ws -> System.out.println("WithholdShareInfo 查询成功: " + ws.getRemark()));
            withholdShareInfoRepository.deleteById(shareInfo.getId());


            // 调用capital接口
            DBCrudDto dto = new DBCrudDto();
            dto.setId(testId);
            finTestService.test(dto);

            return "SUCCESS";

        } catch (Exception e) {
            throw new RuntimeException("数据库CRUD测试失败: " + e.getMessage(), e);
        }
    }


    public String test2(){
        String testId = "TEST0000001";
        WithholdShareInfo shareInfo = new WithholdShareInfo();
        shareInfo.setId(testId);
        shareInfo.setWithholdId("test111");
        shareInfo.setMerchantNo("test111");
        shareInfo.setPrincipal(new BigDecimal("500.00"));
        shareInfo.setInterest(new BigDecimal("50.00"));
        shareInfo.setPenalty(new BigDecimal("50.00"));
        shareInfo.setGuarantee(new BigDecimal("50.00"));
        shareInfo.setConsult(new BigDecimal("50.00"));
        shareInfo.setBreach(new BigDecimal("50.00"));

        shareInfo.setAmount(new BigDecimal("600.00"));
        shareInfo.setCreatedBy("system");
        shareInfo.setCreatedTime(LocalDateTime.now());
        withholdShareInfoRepository.save(shareInfo);
        return "";
    }
}

